"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, Lock, Mail, Shield, Clock, Building2, Users, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function LoginPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [debugInfo, setDebugInfo] = useState("");
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  // Debug para verificar se o JavaScript está carregando
  useEffect(() => {
    setDebugInfo("JavaScript carregado com sucesso!");
    // Usar alert em vez de console.log para debug em produção
    if (typeof window !== 'undefined') {
      (window as any).debugLogin = () => alert("Login page JavaScript está funcionando!");
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setDebugInfo("Formulário submetido!");
    setIsLoading(true);
    setError("");

    try {
      // Fazer a requisição para a API
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setDebugInfo("Login bem-sucedido! Redirecionando...");
        // Redirecionar para o dashboard
        window.location.href = "/dashboard";
      } else {
        setError(data.error || "Erro ao fazer login");
        setDebugInfo("Erro no login: " + (data.error || "Erro desconhecido"));
      }
    } catch (error) {
      setError("Erro de conexão. Tente novamente.");
      setDebugInfo("Erro de conexão: " + error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-40 h-40 bg-teal-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-500"></div>

      <div className="w-full max-w-6xl flex items-center justify-center gap-12 relative z-10">

        {/* Left Side - Features */}
        <div className="hidden lg:flex flex-col space-y-8 text-white max-w-lg">
          <div>
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Sistema Profissional de Controle de Ponto
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed">
              Gerencie a presença dos seus colaboradores com tecnologia de ponta e relatórios avançados.
            </p>
          </div>

          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-teal-500 to-blue-500 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Controle em Tempo Real</h3>
                <p className="text-blue-200">Monitore entradas e saídas instantaneamente</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Building2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Multi-Empresa</h3>
                <p className="text-blue-200">Gerencie múltiplas filiais e departamentos</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Gestão de Equipes</h3>
                <p className="text-blue-200">Organize colaboradores por setores e cargos</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">Relatórios Avançados</h3>
                <p className="text-blue-200">Análises detalhadas e exportação de dados</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full max-w-md">
          {/* Logo e Título */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 via-teal-500 to-blue-700 rounded-2xl mb-6 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              RLPONTO
            </h1>
            <p className="text-blue-200 text-lg font-medium">Sistema de Controle de Ponto Eletrônico</p>
            <p className="text-blue-300 text-sm mt-2">Acesse sua conta para continuar</p>
          </div>

          {/* Card de Login */}
          <Card className="bg-white/95 backdrop-blur-xl shadow-2xl border border-white/20 rounded-3xl transform hover:scale-[1.02] transition-all duration-300">
            <CardHeader className="space-y-1 pb-6 pt-8">
              <CardTitle className="text-2xl font-bold text-center text-slate-800">
                Acesso ao Sistema
              </CardTitle>
              <CardDescription className="text-center text-slate-600 font-medium">
                Digite suas credenciais corporativas para acessar
              </CardDescription>
            </CardHeader>

            <CardContent className="px-8 pb-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Campo Email */}
                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-semibold text-slate-700">
                    Email Corporativo
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-slate-400" />
                    </div>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="pl-10 h-14 border-slate-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-slate-50/50 text-slate-800 placeholder:text-slate-400 font-medium transition-all duration-200"
                      required
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Campo Senha */}
                <div className="space-y-3">
                  <Label htmlFor="password" className="text-sm font-semibold text-slate-700">
                    Senha de Acesso
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-slate-400" />
                    </div>
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Digite sua senha"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="pl-10 pr-12 h-14 border-slate-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-slate-50/50 text-slate-800 placeholder:text-slate-400 font-medium transition-all duration-200"
                      required
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 transition-colors"
                      disabled={isLoading}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                {/* Mensagem de Erro */}
                {error && (
                  <Alert className="border-red-200 bg-red-50/80 backdrop-blur-sm rounded-xl">
                    <div className="flex items-center">
                      <svg className="h-4 w-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <AlertDescription className="text-red-700 font-medium">
                        {error}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}

                {/* Botão de Login */}
                <Button
                  type="submit"
                  className="w-full h-14 bg-gradient-to-r from-blue-600 via-teal-500 to-blue-600 hover:from-blue-700 hover:via-teal-600 hover:to-blue-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-lg"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Autenticando...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                      </svg>
                      <span>Acessar Sistema</span>
                    </div>
                  )}
                </Button>

                {/* Links de Suporte */}
                <div className="text-center space-y-4">
                  <button
                    type="button"
                    className="text-sm text-blue-600 hover:text-blue-700 hover:underline font-medium transition-colors"
                    onClick={() => router.push("/forgot-password")}
                    disabled={isLoading}
                  >
                    Esqueceu sua senha?
                  </button>

                  <div className="flex items-center justify-center space-x-4 text-xs text-slate-500">
                    <span>Suporte: (11) 9999-9999</span>
                    <span>•</span>
                    <span><EMAIL></span>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Debug Info */}
          {debugInfo && (
            <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg">
              <p className="text-sm text-yellow-800 font-medium">Debug: {debugInfo}</p>
            </div>
          )}

          {/* Copyright */}
          <div className="text-center text-sm text-white/70 mt-8">
            <p className="font-medium">RLPONTO v1.0 - Sistema Profissional de Controle de Ponto</p>
            <p className="mt-1">© 2025 RLPONTO. Todos os direitos reservados.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
