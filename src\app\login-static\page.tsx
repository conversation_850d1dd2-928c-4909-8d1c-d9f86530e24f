export default function LoginStaticPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-2xl p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Acesso ao Sistema</h1>
            <p className="text-gray-600">Digite suas credenciais corporativas para acessar</p>
          </div>

          <form action="/api/auth/login" method="POST" className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Corporativo
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Senha de Acesso
              </label>
              <input
                type="password"
                id="password"
                name="password"
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                placeholder="Digite sua senha"
              />
            </div>

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-600 via-teal-500 to-blue-600 hover:from-blue-700 hover:via-teal-600 hover:to-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300"
            >
              Acessar Sistema
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              Problemas para acessar? Entre em contato com o suporte.
            </p>
          </div>
        </div>

        <div className="text-center text-sm text-white/70 mt-8">
          <p className="font-medium">RLPONTO v1.0 - Sistema Profissional de Controle de Ponto</p>
          <p className="mt-1">© 2025 RLPONTO. Todos os direitos reservados.</p>
        </div>
      </div>
    </div>
  );
}
