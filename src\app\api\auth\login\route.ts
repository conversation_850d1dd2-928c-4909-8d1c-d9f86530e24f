import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    let email = "";
    let password = "";

    // Verificar se é JSON ou FormData
    const contentType = request.headers.get("content-type");

    if (contentType?.includes("application/json")) {
      // Requisição JSON (do JavaScript)
      const body = await request.json();
      email = body.email;
      password = body.password;
    } else {
      // Requisição de formulário HTML tradicional
      const formData = await request.formData();
      email = formData.get("email") as string;
      password = formData.get("password") as string;
    }

    console.log("Login attempt:", { email, password: "***" });

    // Por enquanto, aceitar qualquer login para testar
    if (email && password) {
      // Se é formulário HTML, redirecionar
      if (!contentType?.includes("application/json")) {
        return NextResponse.redirect(new URL("/dashboard", request.url));
      }

      // Se é J<PERSON>, retornar resposta JSON
      return NextResponse.json({
        success: true,
        message: "Login realizado com sucesso",
        user: {
          id: "admin-user-id",
          email: email,
          name: "Administrador",
          nivel_acesso: "admin_total",
        },
      });
    }

    // Login inválido
    if (!contentType?.includes("application/json")) {
      return NextResponse.redirect(new URL("/login?error=invalid", request.url));
    }

    return NextResponse.json({
      success: false,
      error: "Email e senha são obrigatórios",
    }, { status: 400 });

  } catch (error) {
    console.error("Erro no login:", error);

    const contentType = request.headers.get("content-type");
    if (!contentType?.includes("application/json")) {
      return NextResponse.redirect(new URL("/login?error=server", request.url));
    }

    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
    }, { status: 500 });
  }
}
